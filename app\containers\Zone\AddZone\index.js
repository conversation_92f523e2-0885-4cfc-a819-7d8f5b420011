import React, { useEffect, useRef, useState } from 'react';
import { Field, Form, Formik } from 'formik';
import { Switch, FormControlLabel } from 'formik-material-ui';
import { ReactSelect } from 'containers/Common/CustomInputs/ReactSelect';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';
import { Alert } from 'react-bootstrap';
import Select from 'react-select';
import { loadEpsList } from 'containers/Eps/EpsList/actions';

import epsReducer from 'containers/Eps/EpsList/reducer';
import epsSaga from 'containers/Eps/EpsList/saga';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import {
  makeSelectCities,
  makeSelectError,
  makeSelectSuccessAddOrUpdateZone,
  makeSelectZone,
} from './selectors';
import { makeSelectEps } from 'containers/Eps/EpsList/selectors';
import zoneReducer from './reducer';
import zoneSaga from './saga';
import {
  addZoneRequest,
  getCitiesRequest,
  loadZone,
  resetAddZone,
  resetError,
  resetUpdateZone,
  updateZoneRequest,
} from './actions';
import { makeSelectAssistantsList } from 'containers/Assistant/Assistants/selectors';
import { getAllAssistantsRequest } from 'containers/Assistant/Assistants/actions';
import assistantsSaga from 'containers/Assistant/Assistants/saga';
import assistantsReducer from 'containers/Assistant/Assistants/reducer';

const keyZoneAdd = 'zoneAdd';

const keyEpsList = 'epsConsult';

const initialValues = {
  isEps: false,
  code: '',
  name: '',
  nameAr: '',
  details: '',
  epsId: null,
  cityIds: [],
  showAssistantInputs: false,
  assistantId: null,
  assistantDateAffectationToZone: '',
  assistantDateEndAffectationToZone: '',
};

const isArabic = text => /[\u0600-\u06FF]/.test(text);

const validationSchema = Yup.object().shape({
  code: Yup.string().required('Le code est requis'),
  name: Yup.string().required('Le nom est requis'),
  nameAr: Yup.string()
    .required('Le nom en arabe est requis')
    .test(
      'is-arabic',
      'Le nom en arabe doit contenir des caractères arabes',
      value => isArabic(value)
    ),
  assistantId: Yup.mixed().when(
    'showAssistantInputs',
    {
      is: true,
      then: Yup.mixed().required('Veuillez sélectionner un assistant'),
      otherwise: Yup.mixed().nullable()
    }
  ),
  assistantDateAffectationToZone: Yup.string().when(
    'showAssistantInputs',
    {
      is: true,
      then: Yup.string().required('Veuillez entrer une date d\'affectation'),
      otherwise: Yup.string().nullable()
    }
  ),
  details: Yup.string().nullable(),
  epsId: Yup.number().nullable(),
  cityIds: Yup.array().nullable(),
  showAssistantInputs: Yup.boolean(),
});


const zoneSelector = createStructuredSelector({
  zone: makeSelectZone,
  error: makeSelectError,
  success: makeSelectSuccessAddOrUpdateZone,
  cities: makeSelectCities,
});
const epsSelector = createStructuredSelector({
  epsListInfos: makeSelectEps,
});

const assistantsSelector = createStructuredSelector({
  assistantsList: makeSelectAssistantsList,
});



const inputStyle = {
  width: '100%',
  padding: '10px',
  margin: '5px 0',
  borderRadius: '35px',
  border: '1px solid #4F89D780',
};

const labelStyle = {
  fontWeight: '600',
  marginBottom: '5px',
};

const dangerStyle = {
  fontSize: '0.9rem',
  marginLeft: '5px',
};

const translateError = (errorDetail, isUpdateMode) => {
  if (isUpdateMode) {
    if (errorDetail.includes('Zone with the same name already exists')) {
      return 'Erreur lors de la modification de la zone : une zone avec le même nom existe déjà';
    } else if (errorDetail.includes('Zone with the same code already exists')) {
      return 'Erreur lors de la modification de la zone : une zone avec le même code existe déjà';
    }
  } else {
    if (errorDetail.includes('Zone with the same name already exists')) {
      console.log('name error',errorDetail);
      return "Erreur lors de l'ajout de la zone : une zone avec le même nom existe déjà";
    } else if (errorDetail.includes('Zone with the same code already exists')) {
      console.log('code error',errorDetail);
      return "Erreur lors de l'ajout de la zone : une zone avec le même code existe déjà";
    }
  }
  return 'Une erreur est survenue';
};

export default function AddZone() {
  useInjectReducer({ key: keyZoneAdd, reducer: zoneReducer });
  useInjectSaga({ key: keyZoneAdd, saga: zoneSaga });

  useInjectReducer({ key: keyEpsList, reducer: epsReducer });
  useInjectSaga({ key: keyEpsList, saga: epsSaga });

  useInjectSaga({ key: 'assistants', saga: assistantsSaga });
  useInjectReducer({ key: 'assistants', reducer: assistantsReducer });

  const { zone, error, success, cities } = useSelector(zoneSelector);
  const { epsListInfos } = useSelector(epsSelector);
  const { assistantsList } = useSelector(assistantsSelector);
  const dispatch = useDispatch();
  const history = useHistory();
  const params = useParams();
  const formikRef = useRef();

  const [showAlert, setShowAlert] = useState(false);
  const [message, setMessage] = useState('');
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isEps, setIsEps] = useState(false);

  const [eps, setEps] = useState(null);
  const [epsName, setEpsName] = useState(null);
  const [cityIds, setCityIds] = useState([]);
  const [epsIdSetter, setEpsIdSetter] = useState(null);
  const [selectedAssistant, setSelectedAssistant] = useState(null);
  const [assistantDateAffectation, setAssistantDateAffectation] = useState('');
  const [assistantDateEndAffectation, setAssistantDateEndAffectation] = useState('');
  const [showAssistantInputs, setShowAssistantInputs] = useState(false);

  const isUpdateMode = Boolean(params.idZone);

  useEffect(() => {
    dispatch(resetAddZone());
    dispatch(resetError());
    dispatch(getCitiesRequest());
    dispatch(loadEpsList());
    
    setShowAlert(false);

  }, [dispatch]);

  useEffect(() => {
    if (!epsListInfos || !Array.isArray(epsListInfos)) {
      return; // Prevents execution if data isn't ready
    }

    const newEpsList = epsListInfos.map((eps) => ({
      label: `${eps.name} - ${eps.code}`,
      id: eps.id,
      name: eps.name,
      nameAr: eps.nameAr,
      comment: eps.comment,
      cityIds: eps.cityIds,
      status: eps.status,
    }));
    setEps(newEpsList);

  }, [epsListInfos,zone]);

  useEffect(() => {
    if (isUpdateMode) { 
      dispatch(loadZone(params.idZone)); 
    } else {
      formikRef.current.setValues(initialValues);
    }
  }, [isUpdateMode, params.idZone, dispatch]);
  // Effect to fetch assistants data
  useEffect(() => {
    dispatch(getAllAssistantsRequest());
  }, [dispatch]);


  useEffect(() => {
    if (zone && isUpdateMode) {
      console.log('zone',zone);
      const hasAssistant = zone.assistantId != null;
      formikRef.current.setValues({
        isEps: zone.epsId ? true : false,
        code: zone.code,
        name: zone.name,
        nameAr: zone.nameAr || '',
        cityIds: zone.cityIds || [],
        details: zone.details,
        showAssistantInputs: hasAssistant,
        assistantId: zone.assistantId || null,
        assistantDateAffectationToZone: zone.assistantDateAffectationToZone || '',
        assistantDateEndAffectationToZone: zone.assistantDateEndAffectationToZone || '',
      });
      setIsEps(zone.epsId ? true : false);
      setEpsName(zone.eps!=null?zone.eps.name:"null");
      if(hasAssistant){
        setSelectedAssistant(zone.assistantId ? { value: zone.assistantId, label: `${zone.assistantName} ` } : null);
        setShowAssistantInputs(true);
        setAssistantDateAffectation(zone.assistantDateAffectationToZone || '');
        setAssistantDateEndAffectation(zone.assistantDateEndAffectationToZone || '');
      }

    }
  }, [zone, isUpdateMode]);

  useEffect(() => {
    if (error && error.detail) {
      const translatedMessage = translateError(error.detail, isUpdateMode);
      setMessage(
        <Alert className="alert-style"
          variant="danger"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{translatedMessage}</p>
        </Alert>,
      );
      setShowAlert(true);
      dispatch(resetError());
    }
  }, [error, isUpdateMode]);

  useEffect(() => {
    if (success && formSubmitted) {
      dispatch(resetAddZone());
      history.push('/zones', {
        successMessage: isUpdateMode
          ? `Zone identifiée par le code ${zone.code} modifiée avec succès !`
          : `Zone identifiée par le code ${zone.code} ajoutée avec succès !`,
      });
      console.log('zone',zone);
      //handleStatusChange(epsIdSetter, false);
      
    }
    dispatch(resetAddZone());
    dispatch(resetUpdateZone());
  }, [success, formSubmitted, isUpdateMode, history, dispatch]);

  useEffect(() => {
    if (showAlert) {
      setTimeout(() => {
        setShowAlert(false);
      }, 4000);
    }
  }, [showAlert]);

  const handleSubmit = async (values, { setSubmitting, validateForm, setErrors }) => {
    if (isEps) {
      values.epsId = values.eps ? epsIdSetter : null;
    }

    // Conditionally include assistant values
    if (showAssistantInputs) {
      values.assistantId = selectedAssistant ? selectedAssistant.value : null;
      values.assistantDateAffectationToZone = assistantDateAffectation || null;
      values.assistantDateEndAffectationToZone = assistantDateEndAffectation || null;
    } else {
      // Set assistant values to null if inputs are hidden
      values.assistantId = null;
      values.assistantDateAffectationToZone = null;
      values.assistantDateEndAffectationToZone = null;
    }

    // Validate the form manually before submitting
    const formErrors = await validateForm(values);

    if (Object.keys(formErrors).length > 0) {
      // If there are errors, set them in Formik state and stop submission
      setErrors(formErrors); // Explicitly set errors
      setSubmitting(false);
      setFormSubmitted(false); // Reset formSubmitted so success message doesn't show prematurely
      console.log('Validation errors:', formErrors);
      return; // Prevent submission
    }

    setSubmitting(true);
    setFormSubmitted(true);
    if (isUpdateMode) {
      dispatch(updateZoneRequest(params.idZone, values));
    } else {
      dispatch(addZoneRequest(values));
    }
  };


  const handleCancel = () => {
    history.push('/zones');
  };

  const cityOptions = cities.map(city => ({
    value: city.id,
    label: city.name,
  }));

  return (
    <div className="sub-container mx-auto my-4" style={{maxWidth: '700px'}}>
      {showAlert && message}
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        innerRef={formikRef}
      >
        {({ isSubmitting, errors, touched, setFieldValue, values, setFieldTouched }) => (
          <Form>
            <h3 style={{ textAlign: 'center', marginBottom: '20px' }}>
              {isUpdateMode ? 'Modifier la zone' : 'Ajouter une zone'}
            </h3>
            <div className="form-group">

              <label className={`mr-2 pt-1}`}>
                S'agit d'un EPS ?
              </label>
              <Field
                component={Switch}
                type="checkbox"
                disabled={isUpdateMode?true:false}
                name="isEps"
                onClick={() => {
                  setFieldValue('eps.name', "");
                  setFieldValue('eps.nameAr', "");
                  setFieldValue('eps.comment', "");

                  setFieldValue('eps.cityIds', "");
                  setCityIds("");
                  setEpsName("");
                  setIsEps(!isEps)
                  setFieldValue('isEps', isEps)
                  setEpsIdSetter(null);
                }}
              />
              {isEps && eps != null && (
                <div className="form-group col-md-12 mt-1">
                  <ReactSelect
                    field="eps"
                    name="eps.label"
                    placeholder="Choisir un EPS"
                    isDisabled={isUpdateMode?true:false}
                    isClearable
                    formProps={eps}
                    value={
                      (!isUpdateMode?eps.filter(option => option.name === epsName):eps.filter(option => option.name === epsName))
                    }
                    options={(!isUpdateMode?eps.filter(option => option.status === false):eps)}
                    onChange={(e) => {
                      e = e ? e : { name: '', nameAr: '', comment: '', cityIds: [] };
                      setFieldValue('name', e.name ? e.name : '');
                      setFieldValue('nameAr', e.nameAr ? e.nameAr : '');
                      setFieldValue('details', e.comment !== null ? e.comment : '');
                      setFieldValue('cityIds', e.cityIds !== null ? e.cityIds : '');
                      setEpsName(e.name);
                      setEpsIdSetter(e.id);
                    }}

                    noOptionsMessage={() =>
                      (
                        <span>
                          Pas de eps disponible, {" "}
                          <a href="/eps/addEps" style={{ color: "blue", textDecoration: "underline", cursor: "pointer" }}>
                            veuillez ajouter un eps
                          </a>
                        </span>
                      )
                    } />
                </div>
                )}

              <h3></h3>
              <label htmlFor="code" style={labelStyle}>
                Code de la zone <span className="text-danger">*</span>
              </label>
              <Field
                id="code"
                name="code"
                disabled={isUpdateMode?true:false}
                type="text"
                className="form-control"
                placeholder="Code de la zone"
                style={inputStyle}
              />
              {errors.code && touched.code && (
                <div style={dangerStyle} className="text-danger">
                  {errors.code}
                </div>
              )}
            </div>
            <label htmlFor="name" style={labelStyle}>
              Nom de la zone <span className="text-danger">*</span>
            </label>
            <Field
              id="name"
              name="name"
              type="text"
              className="form-control"
              placeholder="Nom de la zone"
              style={inputStyle}
            />
            {errors.name && touched.name && (
              <div style={dangerStyle} className="text-danger">
                {errors.name}
              </div>
            )}

            <div className="form-group">
              <label
                htmlFor="nameAr"
                style={{
                  ...labelStyle,
                  textAlign: 'right',
                  direction: 'rtl',
                  float: 'right',
                }}
              >
                اسم المنطقة <span className="text-danger">*</span>
              </label>
              <Field
                id="nameAr"
                name="nameAr"
                type="text"
                className="form-control"
                placeholder="اسم المنطقة"
                style={{ ...inputStyle, direction: 'rtl' }} // Set direction to RTL for Arabic input
                dir="rtl" // Explicitly set the direction attribute to RTL
              />
              {errors.nameAr && touched.nameAr && (
                <div style={dangerStyle} className="text-danger">
                  {errors.nameAr}
                </div>
              )}
            </div>
            <div className="form-group">
              <label htmlFor="cityIds" style={labelStyle}>
                Villes associées
              </label>
              <Select
                id="cityIds"
                placeholder="Sélectionnez les villes associées"
                isMulti
                name="cityIds"
                options={cityOptions}
                value={
                  cityOptions.filter(option =>
                    values.cityIds.includes(cityIds) || values.cityIds.includes(option.value),
                  )}
                onChange={options =>
                  setFieldValue(
                    'cityIds',
                    options.map(option => option.value),
                  )
                }
                styles={{
                  control: provided => ({
                    ...provided,
                    borderRadius: '35px',
                    borderColor: '#4F89D780',
                  }),
                  multiValue: provided => ({
                    ...provided,
                    borderRadius: '20px',
                    padding: '0 5px',
                  }),
                  multiValueRemove: (provided) => ({
                    ...provided,
                    borderRadius: '50%',
                  }),
                }}
                className="basic-multi-select"
                classNamePrefix="select"
                noOptionsMessage={() => 'Aucune option disponible'}
              />
            </div>
            <div className="form-group">
              <label className={`mr-2 pt-1}`}>
                Associer un assistant ?
              </label>
              <Field
                component={Switch}
                type="checkbox"
                name="showAssistantInputs"
                checked={showAssistantInputs}
                onChange={() => {
                  const newValue = !showAssistantInputs;
                  setShowAssistantInputs(newValue);
                  setFieldValue('showAssistantInputs', newValue);
                  if (!newValue) {
                    // Reset assistant fields when unchecked
                    setSelectedAssistant(null);
                    setAssistantDateAffectation('');
                    setAssistantDateEndAffectation('');
                    setFieldValue('assistantId', null);
                    setFieldValue('assistantDateAffectationToZone', '');
                    setFieldValue('assistantDateEndAffectationToZone', '');
                  }
                }}
              />
            </div>

            {showAssistantInputs && (
              <>
                <div className="form-group">
                  <label htmlFor="assistant" style={labelStyle}>
                    Assistant associé <span className="text-danger">*</span>
                  </label>
                  <Select
                    id="assistant"
                    placeholder="Sélectionnez un assistant"
                    isClearable
                    options={assistantsList && assistantsList.content ? assistantsList.content.map(assistant => ({ value: assistant.id, label: `${assistant.firstName} ${assistant.lastName}` })) : []}
                    value={selectedAssistant}
                    onChange={(selectedOption) => {
                      setSelectedAssistant(selectedOption);
                      setAssistantDateAffectation('');
                      setAssistantDateEndAffectation('');
                      setFieldValue('assistantId', selectedOption ? selectedOption.value : null);
                      setFieldTouched('assistantId', true);
                    }}
                    styles={{
                      control: provided => ({
                        ...provided,
                        borderRadius: '35px',
                        borderColor: '#4F89D780',
                      }),
                      multiValue: provided => ({
                        ...provided,
                        borderRadius: '20px',
                        padding: '0 5px',
                      }),
                      multiValueRemove: (provided) => ({
                        ...provided,
                        borderRadius: '50%',
                      }),
                    }}
                    className="basic-multi-select"
                    classNamePrefix="select"
                    noOptionsMessage={() => 'Aucune option disponible'}
                  />
                  {/* Error display for assistantId */}
                  {errors.assistantId && touched.assistantId && (
                    <div style={dangerStyle} className="text-danger">
                      {errors.assistantId}
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="assistantDateAffectationToZone" style={labelStyle}>
                    Date d'affectation de l'assistant <span className="text-danger">*</span>
                  </label>
                  <Field 
                    id="assistantDateAffectationToZone"
                    name="assistantDateAffectationToZone"
                    type="date"
                    className="form-control"
                    style={inputStyle}
                    value={assistantDateAffectation}
                    onChange={(e) => {
                      setAssistantDateAffectation(e.target.value);
                      setFieldValue('assistantDateAffectationToZone', e.target.value);
                      setFieldTouched('assistantDateAffectationToZone', true);
                    }}
                  />
                  {/* Error display for assistantDateAffectationToZone */}
                  {errors.assistantDateAffectationToZone && touched.assistantDateAffectationToZone && (
                    <div style={dangerStyle} className="text-danger">
                      {errors.assistantDateAffectationToZone}
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="assistantDateEndAffectationToZone" style={labelStyle}>
                    Date de fin d'affectation de l'assistant
                  </label>
                  <Field
                    id="assistantDateEndAffectationToZone"
                    name="assistantDateEndAffectationToZone"
                    type="date"
                    className="form-control"
                    style={inputStyle}
                    value={assistantDateEndAffectation}
                    onChange={(e) => {
                      setAssistantDateEndAffectation(e.target.value);
                      setFieldValue('assistantDateEndAffectationToZone', e.target.value);
                      setFieldTouched('assistantDateEndAffectationToZone', true);
                    }}
                  />
                </div>
              </>
            )}

            

            <div className="form-group">
              <label htmlFor="details" style={labelStyle} >
                Commentaire
              </label>
              <Field
                id="details"
                name="details"
                as="textarea"
                className="form-control"
                placeholder="Commentaire"
                style={inputStyle}
              />
            </div>

            <div className="d-flex align-items-center justify-content-end gap-10">
              <button
                  type="button"
                  className="btn-style secondary"
                  onClick={handleCancel}
              >
                Annuler
              </button>
              <button
                type="submit"
                className="btn-style primary"
                disabled={isSubmitting}
              >
                {isUpdateMode ? 'Modifier' : 'Ajouter'}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}
